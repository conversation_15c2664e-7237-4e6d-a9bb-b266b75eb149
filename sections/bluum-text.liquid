{%- if section.blocks.size > 0 -%}
  {%- render 'section-spacing-collapsing' -%}

  {%- comment -%}
  ------------------------------------------------------------------------------------------------------------------------
  CSS
  ------------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}

  <style>
    #shopify-section-{{ section.id }} {
      margin-top: {{ section.settings.margin_top }}px;
      --review-spacing: 24px;
      --star-size: {{ section.settings.star_size_mobile }}px;
      --bluum-text-font-size: calc(min(20vw, 65px) * {{ section.settings.bluum_text_size_ratio_mobile }});
      --bluum-text-line-height: {{ section.settings.bluum_text_line_height }}%;
      --bluum-text-auto-columns: {% if section.blocks.size == 1 %}minmax(0, 1fr){% else %}{% if section.settings.text_divider != 'none' %}48vw auto{% else %}64vw{% endif %}{% endif %};
      --bluum-text-max-width: {{ section.settings.content_max_width_mobile }}px;
      --bluum-text-button-spacing: {{ section.settings.button_spacing }}px;
    }

    .bluum-text__reviews {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      margin-bottom: var(--review-spacing);
    }

    .bluum-text__stars {
      display: flex;
      gap: 0;
    }

    .bluum-text__star {
      width: var(--star-size);
      height: var(--star-size);
      color: var(--button-background);
      margin-inline-start: -4px;
    }

    .bluum-text__star:first-child {
      margin-inline-start: 0;
    }

    .bluum-text__review-count {
      color: rgb(var(--text-color));
      opacity: 0.85;
      font-family: var(--text-font-family);
      font-size: var(--text-h5);
      font-weight: var(--text-font-weight);
      line-height: 1.2;
      letter-spacing: var(--text-letter-spacing);
    }

    .bluum-text__text {
      line-height: var(--bluum-text-line-height);
      max-width: var(--bluum-text-max-width);
      margin-left: auto;
      margin-right: auto;
    }

    .bluum-text__content .prose .button {
      margin-block-start: var(--bluum-text-button-spacing) !important;
    }

    @media screen and (min-width: 700px) {
      #shopify-section-{{ section.id }} {
        --star-size: {{ section.settings.star_size_desktop }}px;
        --bluum-text-font-size: calc(min(15vw, var(--container-max-width) * 0.15) / {{ section.blocks.size }} * {{ section.settings.bluum_text_size_ratio_desktop }});
        --bluum-text-auto-columns: {% if section.settings.text_divider != 'none' %}minmax(0, 1fr) auto{% else %}minmax(0, 1fr){% endif %};
        --bluum-text-max-width: {{ section.settings.content_max_width_desktop }}px;
      }
    }
  </style>

  {%- comment -%}
  ------------------------------------------------------------------------------------------------------------------------
  LIQUID
  ------------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}

  <div {% render 'section-properties' %}>
    <div {% unless section.settings.stack_mobile %}class="scroll-area bleed {% if section.blocks.size < 3 %}sm:unbleed{% else %}md:unbleed{% endif %}"{% endunless %}>
      <div class="bluum-text bluum-text--{{ section.settings.text_alignment }} {% unless section.settings.stack_mobile %}bluum-text--scroll{% endunless %}">
          {%- for block in section.blocks -%}
            <div class="snap-center" {{ block.shopify_attributes }}>
              {%- if block.settings.show_reviews -%}
                <div class="bluum-text__reviews">
                  <div class="bluum-text__stars">
                    {%- for i in (1..5) -%}
                      <svg class="bluum-text__star" viewBox="0 0 24 24" fill="currentColor"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                    {%- endfor -%}
                  </div>
                  <p class="bluum-text__review-count heading heading--small">{{ block.settings.review_text }}</p>
                </div>
              {%- endif -%}
              {%- if block.settings.title != blank -%}
                <h2 class="bluum-text__text heading break-all">
                  <bluum-text {% if block.settings.animate_bluum_text %}count-up="{{ block.settings.animate_bluum_text_duration }}"{% endif %} reveal-js>
                    {%- render 'styled-text', content: block.settings.title, text_color: section.settings.heading_text_color, gradient: section.settings.heading_gradient, style: section.settings.bluum_text_style -%}
                  </bluum-text>
                </h2>
              {%- endif -%}

              {%- if block.settings.subheading != blank or block.settings.content != blank or block.settings.button_text != blank -%}
                <div class="bluum-text__content">
                  <div class="prose">
                    {%- if block.settings.subheading != blank -%}
                      <h3 class="h4">{{ block.settings.subheading | escape }}</h3>
                    {%- endif -%}

                    {{- block.settings.content | richtext -}}

                    {%- if block.settings.button_text != blank -%}
                      {%- render 'button', content: block.settings.button_text, href: block.settings.button_url, size: 'xl', background: section.settings.button_background, text_color: section.settings.button_text_color -%}
                    {%- endif -%}
                  </div>
                </div>
              {%- endif -%}
            </div>

            {%- unless forloop.last or section.settings.text_divider == 'none' -%}
              {%- case section.settings.text_divider -%}
                {%- when 'square' -%}
                  <span class="shape-square shape--lg place-self-center"></span>
                {%- when 'circle' -%}
                  <span class="shape-circle shape--lg place-self-center"></span>
                {%- when 'diamond' -%}
                  <span class="shape-diamond shape--lg place-self-center"></span>
                {%- when 'line' -%}
                  <span class="shape-line {% if section.settings.stack_mobile %}hidden sm:block{% endif %}"></span>
              {%- endcase -%}
            {%- endunless -%}
          {%- endfor -%}
      </div>
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "bluum text",
  "class": "shopify-section--bluum-text",
  "tag": "section",
  "disabled_on": {
    "templates": ["password"],
    "groups": ["header", "custom.overlay"]
  },
  "max_blocks": 3,
  "blocks": [
    {
      "type": "item",
      "name": "Item",
      "settings": [
        {
          "type": "header",
          "content": "Reviews"
        },
        {
          "type": "checkbox",
          "id": "show_reviews",
          "label": "Show reviews",
          "default": false
        },
        {
          "type": "text",
          "id": "review_text",
          "label": "Review text",
          "default": "Compounds trusted by 1000+"
        },
        {
          "type": "richtext",
          "id": "title",
          "label": "bluum text",
          "info": "For best results, keep this text short and bluumful.",
          "default": "<p>100%</p>"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Subheading",
          "default": "Showcase a benefit of your product"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Button text"
        },
        {
          "type": "url",
          "id": "button_url",
          "label": "Button link"
        },
        {
          "type": "header",
          "content": "Number animation"
        },
        {
          "type": "paragraph",
          "content": "Only number can be animated. Dots, commas and spaces can be used as separators."
        },
        {
          "type": "checkbox",
          "id": "animate_bluum_text",
          "label": "Show count up animation",
          "default": false
        },
        {
          "type": "range",
          "id": "animate_bluum_text_duration",
          "label": "Count up duration",
          "min": 0.1,
          "max": 5,
          "step": 0.1,
          "unit": "s",
          "default": 2
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "header",
      "content": "Reviews"
    },
    {
      "type": "range",
      "id": "star_size_mobile",
      "min": 20,
      "max": 60,
      "step": 4,
      "unit": "px",
      "label": "Star size on mobile",
      "default": 32
    },
    {
      "type": "range",
      "id": "star_size_desktop",
      "min": 20,
      "max": 60,
      "step": 4,
      "unit": "px",
      "label": "Star size on desktop",
      "default": 40
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": -100,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Margin top",
      "default": 0
    },
    {
      "type": "range",
      "id": "button_spacing",
      "label": "Button spacing",
      "min": 8,
      "max": 48,
      "step": 4,
      "unit": "px",
      "default": 24
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Full width",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "stack_mobile",
      "label": "Stack on mobile",
      "default": true
    },
    {
      "type": "range",
      "id": "content_max_width_mobile",
      "label": "Mobile max width",
      "min": 300,
      "max": 800,
      "step": 20,
      "unit": "px",
      "default": 500
    },
    {
      "type": "range",
      "id": "content_max_width_desktop",
      "label": "Desktop max width",
      "min": 400,
      "max": 1600,
      "step": 50,
      "unit": "px",
      "default": 800
    },
    {
      "type": "header",
      "content": "Text settings"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "options": [
        {
          "value": "start",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "end",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "bluum_text_style",
      "label": "bluum text style",
      "options": [
        {
          "value": "outline",
          "label": "Outline"
        },
        {
          "value": "fill",
          "label": "Fill"
        }
      ],
      "default": "fill"
    },
    {
      "type": "select",
      "id": "text_divider",
      "label": "Multiple texts divider",
      "options": [
        {
          "value": "none",
          "label": "None"
        },
        {
          "value": "square",
          "label": "Square"
        },
        {
          "value": "circle",
          "label": "Circle"
        },
        {
          "value": "diamond",
          "label": "Diamond"
        },
        {
          "value": "line",
          "label": "Line"
        }
      ],
      "default": "none"
    },
    {
      "type": "range",
      "id": "bluum_text_size_ratio_mobile",
      "label": "Mobile bluum text size ratio",
      "min": 0.1,
      "max": 1.5,
      "step": 0.1,
      "default": 1
    },
    {
      "type": "range",
      "id": "bluum_text_size_ratio_desktop",
      "label": "Desktop bluum text size ratio",
      "min": 0.1,
      "max": 1.5,
      "step": 0.1,
      "default": 1
    },
    {
      "type": "range",
      "id": "bluum_text_line_height",
      "label": "bluum text line height",
      "min": 100,
      "max": 200,
      "step": 5,
      "unit": "%",
      "default": 120
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Gradient replaces solid colors when set. Gradient text outline and gradient background cannot be combined."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Background gradient"
    },
    {
      "type": "color",
      "id": "heading_text_color",
      "label": "bluum text"
    },
    {
      "type": "color_background",
      "id": "heading_gradient",
      "label": "bluum text gradient"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text"
    }
  ],
  "presets": [
    {
      "name": "bluum text",
      "blocks": [
        {
          "type": "item"
        }
      ]
    }
  ]
}
{% endschema %}
