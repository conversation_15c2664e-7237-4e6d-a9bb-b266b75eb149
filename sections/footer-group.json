/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "type": "footer",
  "name": "Footer group",
  "sections": {
    "text-with-icons": {
      "type": "text-with-icons",
      "blocks": {
        "free-shipping": {
          "type": "item",
          "settings": {
            "icon": "picto-box",
            "mobile_icon_width": 24,
            "icon_width": 24,
            "icon_background_type": "none",
            "icon_background": "",
            "icon_color": "",
            "title": "Free shipping",
            "content": "<p>Tell your customers about your shipping offer.</p>"
          }
        },
        "customer-support": {
          "type": "item",
          "settings": {
            "icon": "picto-customer-support",
            "mobile_icon_width": 24,
            "icon_width": 24,
            "icon_background_type": "none",
            "icon_background": "",
            "icon_color": "",
            "title": "Customer service",
            "content": "<p>Tell your customers how they can reach you.</p>"
          }
        },
        "discount": {
          "type": "item",
          "settings": {
            "icon": "picto-coupon",
            "mobile_icon_width": 24,
            "icon_width": 24,
            "icon_background_type": "none",
            "icon_background": "",
            "icon_color": "",
            "title": "Refer a friend",
            "content": "<p>Tell your customers about your promotional offers.</p>"
          }
        },
        "payment": {
          "type": "item",
          "settings": {
            "icon": "picto-lock",
            "mobile_icon_width": 24,
            "icon_width": 24,
            "icon_background_type": "none",
            "icon_background": "",
            "icon_color": "",
            "title": "Secure payment",
            "content": "<p>Tel your customers about your payment methods.</p>"
          }
        }
      },
      "block_order": [
        "free-shipping",
        "customer-support",
        "discount",
        "payment"
      ],
      "disabled": true,
      "settings": {
        "full_width": true,
        "stack_on_mobile": false,
        "title": "",
        "heading_tag": "h6",
        "text_alignment": "center",
        "icon_spacing": "small",
        "background": "",
        "background_gradient": "",
        "text_color": ""
      }
    },
    "footer": {
      "type": "footer",
      "blocks": {
        "newsletter": {
          "type": "newsletter",
          "settings": {
            "image": "shopify://shop_images/bluum-logo.png",
            "image_width": 300,
            "heading_size": "h3",
            "title": "",
            "content": "<p>Stay in the loop on the latest products and discounts.</p>"
          }
        },
        "links": {
          "type": "links",
          "settings": {
            "menu": "footer",
            "show_menu_title": false,
            "menu_title": "Links"
          }
        },
        "links_N6danA": {
          "type": "links",
          "settings": {
            "menu": "footer-right",
            "show_menu_title": false,
            "menu_title": ""
          }
        },
        "text": {
          "type": "text",
          "disabled": true,
          "settings": {
            "title": "<p>About</p>",
            "content": "<p>Use this text area to tell your customers about your brand and vision. You can change it in the theme editor.</p>",
            "enable_follow_on_shop": true
          }
        }
      },
      "block_order": [
        "newsletter",
        "links",
        "links_N6danA",
        "text"
      ],
      "settings": {
        "show_social_media": true,
        "show_payment_icons": true,
        "show_country_selector": true,
        "show_country_flag": true,
        "show_country_name": true,
        "show_locale_selector": true
      }
    }
  },
  "order": [
    "text-with-icons",
    "footer"
  ]
}
