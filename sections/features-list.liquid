{%- style -%}
  .section.features-list.section-{{ section.id }}-padding {
    margin-top: {{ section.settings.margin_top }}px;
    margin-bottom: {{ section.settings.margin_bottom }}px;
    padding-block-start: {{ section.settings.padding_block_start | times: 0.75 | round: 0 }}px !important;
    padding-block-end: {{ section.settings.padding_block_end | times: 0.75 | round: 0 }}px !important;
  }

  @media screen and (min-width: 750px) {
    .section.features-list.section-{{ section.id }}-padding {
      padding-block-start: {{ section.settings.padding_block_start }}px !important;
      padding-block-end: {{ section.settings.padding_block_end }}px !important;
    }
  }

  /* Features List Styles */
  .features-list {
    --features-list-gap: var(--spacing-6);
    background: rgb(var(--background-primary) / 0.5);
    position: relative;
    isolation: isolate;
    padding-inline-start: 0;
    padding-inline-end: 0;
  }

  .features-list:before {
    content: '';
    position: absolute;
    inset: 0;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    z-index: -1;
  }

  .features-list__wrapper {
    margin: 0 auto;
    padding-inline-start: 0;
    padding-inline-end: 0;
  }

  @media screen and (min-width: 750px) {
    .features-list__wrapper {
      margin-right: 0;
      padding-right: var(--spacing-16);
      max-width: none;
    }
  }

  .features-list__container {
    display: grid;
    gap: var(--features-list-gap);
  }

  .features-list__content-container {
    display: grid;
    gap: var(--spacing-2);
    grid-template-columns: 1fr;
  }

  .features-list__image-area {
    position: relative;
    aspect-ratio: 3/4;
    background: rgb(var(--background-primary));
    border-radius: var(--rounded-lg);
    overflow: hidden;
    order: -1;
    margin-bottom: var(--spacing-4);
  }

  .features-list__image-wrapper {
    opacity: 0;
    visibility: hidden;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
  }

  .features-list__image-wrapper.is-active {
    opacity: 1;
    visibility: visible;
  }

  .features-list__main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--rounded-sm);
  }

  .features-list__text-area {
    display: grid;
    gap: var(--spacing-4);
  }

  .features-list__button {
    text-align: left;
    background: rgb(var(--background-primary) / 0.7);
    padding-left: var(--spacing-4);
    padding-right: var(--spacing-4);
    padding-top: var(--spacing-8);
    padding-bottom: var(--spacing-8);
    border-radius: var(--rounded-lg);
    transition: border-color 0.2s ease-in-out, background-color 0.2s ease-in-out, transform 0.2s ease-in-out;
    width: 100%;
    position: relative;
    isolation: isolate;
    opacity: 0.5;
  }

  .features-list__button.is-active {
    opacity: 1;
  }

  .features-list__button:before {
    content: '';
    position: absolute;
    inset: 0;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: inherit;
    z-index: -1;
    background: #EAEAEA;
  }

  .features-list__button:hover {
    transform: translateY(-2px);
  }

  .features-list__button:hover,
  .features-list__button.is-active {
    border-color: rgb(var(--text-color));
    background: #EAEAEA;
  }

  .features-list__button-content {
    display: flex;
    gap: var(--spacing-2);
    align-items: flex-start;
  }

  @media screen and (min-width: 750px) {
    .features-list__button-content {
      gap: var(--spacing-8);
    }
  }

  .features-list__icon {
    flex-shrink: 0;
    width: var(--spacing-12);
    height: var(--spacing-12);
    display: grid;
    place-items: center;
    border-radius: var(--rounded-lg);
    padding-left: var(--spacing-3);
    padding-right: var(--spacing-3);
    padding-bottom: var(--spacing-14);
    transition: background-color 0.2s ease-in-out;
    margin-top: var(--spacing-1);
  }

  @media screen and (min-width: 750px) {
    .features-list__icon {
      padding-bottom: var(--spacing-6);
    }
  }

  .features-list__icon img {
    width: 100%;
    height: 100%;
  }

  .features-list__text-content {
    display: grid;
    gap: var(--spacing-2);
  }

  .features-list__button-title {
    font-size: var(--text-h3);
    font-weight: 500;
    letter-spacing: -.02em;
  }

  .features-list__text {
    font-weight: normal; 
    font-size: var(--text-base);
    line-height: 1.6;
    margin-top: var(--spacing-2);
    max-width: 42ch;
    height: 0;
    opacity: 0;
    overflow: hidden;
    transition: height 0.3s ease-in-out, opacity 0.3s ease-in-out;
  }

  .features-list__button.is-active .features-list__text {
    height: auto;
    opacity: 1;
  }

  @media screen and (min-width: 750px) {
    .features-list {
      --features-list-gap: var(--spacing-8);
    }

    .features-list__content-container {
      grid-template-columns: minmax(0, 1fr) minmax(0, 1fr);
      gap: var(--spacing-6);
      align-items: center;
    }

    .features-list__image-area {
      order: 2;
      margin-bottom: 0;
      height: 100%;
      display: flex;
      align-items: center;
    }

    .features-list__button {
      padding-left: var(--spacing-6);
      padding-right: var(--spacing-6);
      padding-top: var(--spacing-8);
      padding-bottom: var(--spacing-8);
    }

    .features-list__text-area {
      display: grid;
      gap: var(--spacing-6);
    }

    .features-list__icon {
      width: var(--spacing-14);
      height: var(--spacing-14);
    }

    .features-list__button-title {
      font-size: var(--text-h3);
    }
  }

  @media screen and (min-width: 1000px) {
    .features-list__content-container {
      gap: var(--spacing-6);
    }
  }
{%- endstyle -%}

{{ 'features-list.js' | asset_url | script_tag }}

<div data-features-list class="section features-list section-{{ section.id }}-padding{% if section.settings.title == blank %} no-heading{% endif %}">
  <div class="features-list__wrapper page-width">
    <div class="features-list__container">
      <div class="features-list__content-container">
        <div class="features-list__text-area">
          {%- for block in section.blocks -%}
            <button class="features-list__button" data-index="{{ forloop.index }}" {{ block.shopify_attributes }}>
              <div class="features-list__button-content">
                <div class="features-list__icon">
                  {%- case forloop.index -%}
                    {%- when 1 -%}
                      <img src="{{ 'icon-flask.svg' | asset_url }}" alt="" class="icon" width="24" height="24" loading="lazy">
                    {%- when 2 -%}
                      <img src="{{ 'icon-plane.svg' | asset_url }}" alt="" class="icon" width="24" height="24" loading="lazy">
                    {%- when 3 -%}
                      <img src="{{ 'icon-molecule.svg' | asset_url }}" alt="" class="icon" width="24" height="24" loading="lazy">
                  {%- endcase -%}
                </div>
                <div class="features-list__text-content">
                  <span class="features-list__button-title h3">{{ block.settings.title | escape }}</span>
                  {%- if block.settings.text != blank -%}
                    <div class="features-list__text rte h4">{{ block.settings.text }}</div>
                  {%- endif -%}
                </div>
              </div>
            </button>
          {%- endfor -%}
        </div>
        <div class="features-list__image-area">
          {%- for block in section.blocks -%}
            {%- if block.settings.image != blank -%}
              <div class="features-list__image-wrapper" data-index="{{ forloop.index }}">
                <img 
                  src="{{ block.settings.image | image_url: width: 800 }}"
                  alt="{{ block.settings.title | escape }}"
                  loading="lazy"
                  class="features-list__main-image"
                >
              </div>
            {%- endif -%}
          {%- endfor -%}
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Features List",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "range",
      "id": "padding_block_start",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding block start",
      "default": 16
    },
    {
      "type": "range",
      "id": "padding_block_end",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding block end",
      "default": 16
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": -100,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Margin top",
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "min": -100,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Margin bottom",
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "feature",
      "name": "Feature",
      "limit": 3,
      "settings": [
        {
          "type": "image_picker",
          "id": "icon",
          "label": "Icon"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Feature Image"
        },
        {
          "type": "text",
          "id": "title",
          "default": "Feature title",
          "label": "Title"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Share information about a key feature of your product.</p>",
          "label": "Text"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Features List",
      "blocks": [
        {
          "type": "feature"
        },
        {
          "type": "feature"
        },
        {
          "type": "feature"
        }
      ]
    }
  ]
}
{% endschema %}
