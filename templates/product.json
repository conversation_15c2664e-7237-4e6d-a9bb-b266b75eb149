/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-product",
      "blocks": {
        "title": {
          "type": "title",
          "settings": {
            "heading_tag": "h2"
          }
        },
        "badges": {
          "type": "badges",
          "settings": {}
        },
        "price": {
          "type": "price",
          "settings": {
            "show_taxes_notice": false
          }
        },
        "variant_picker": {
          "type": "variant_picker",
          "settings": {
            "hide_sold_out_variants": false,
            "stack_blocks": true,
            "selector_style": "block",
            "swatch_selector_style": "swatch",
            "variant_image_options": "",
            "size_chart_page": ""
          }
        },
        "quantity_selector": {
          "type": "quantity_selector",
          "settings": {}
        },
        "buy_buttons": {
          "type": "buy_buttons",
          "settings": {
            "show_payment_button": false,
            "show_gift_card_recipient": true,
            "atc_button_background": "",
            "atc_button_text_color": "",
            "payment_button_background": "",
            "payment_button_text_color": ""
          }
        },
        "offer_NH3yPe": {
          "type": "offer",
          "settings": {
            "text_alignment": "center",
            "icon_position": "aligned",
            "icon": "picto-box",
            "icon_width": 24,
            "title": "FREE SHIPPING",
            "content": "",
            "background": "#eaeaea",
            "text_color": "#24201f"
          }
        },
        "offer_Rn3tCi": {
          "type": "offer",
          "settings": {
            "text_alignment": "center",
            "icon_position": "aligned",
            "icon": "picto-validation",
            "icon_width": 24,
            "title": "LAB-TESTED",
            "content": "",
            "background": "#eaeaea",
            "text_color": "#24201f"
          }
        },
        "rating": {
          "type": "rating",
          "disabled": true,
          "settings": {
            "show_empty": false
          }
        },
        "payment_terms": {
          "type": "payment_terms",
          "settings": {}
        },
        "text_RYM3iC": {
          "type": "text",
          "settings": {
            "text": "<p><strong>RESEARCH USE ONLY</strong><br/>These compounds are NOT  intended for human consumption, clinical use, or veterinary applications. We are not affiliated with any pharmaceutical companies or their commercial medications. By placing an order, you certify these materials will be used exclusively for in vitro testing and laboratory experimentation only. Bodily introduction of any kind into humans or animals is strictly forbidden by law. This product should only be handled by licensed, qualified professionals. This product is not a drug, food, or cosmetic and may not be misbranded, misused or mislabeled as a drug, food or cosmetic.</p>"
          }
        },
        "description": {
          "type": "description",
          "settings": {
            "collapse_content": true
          }
        }
      },
      "block_order": [
        "title",
        "badges",
        "price",
        "variant_picker",
        "quantity_selector",
        "buy_buttons",
        "offer_NH3yPe",
        "offer_Rn3tCi",
        "rating",
        "payment_terms",
        "text_RYM3iC",
        "description"
      ],
      "custom_css": [
        ".product-info__text {background: #eaeaea; padding: 40px; border-radius: 40px;}"
      ],
      "settings": {
        "full_width": true,
        "show_fixed_add_to_cart": true,
        "desktop_media_width": 50,
        "desktop_media_layout": "grid_highlight",
        "mobile_media_size": "expanded",
        "mobile_carousel_control": "dots",
        "enable_video_autoplay": false,
        "enable_video_looping": true,
        "enable_image_zoom": true,
        "max_image_zoom_level": 3,
        "background": "",
        "background_gradient": "",
        "text_color": "",
        "input_background": "rgba(0,0,0,0)",
        "input_text_color": ""
      }
    },
    "related-products": {
      "type": "related-products",
      "custom_css": [
        ".product-info__text {background: #eaeaea; padding: 40px; border-radius: 40px;}"
      ],
      "settings": {
        "recommendations_count": 6,
        "products": [],
        "full_width": true,
        "stack_products": false,
        "show_progress_bar": false,
        "products_per_row_mobile": "2",
        "products_per_row_desktop": 5,
        "subheading": "",
        "title": "You may also like",
        "content": "",
        "background": "",
        "background_gradient": "",
        "text_color": "",
        "heading_color": "",
        "heading_gradient": "",
        "product_card_background": "",
        "product_card_text_color": ""
      }
    }
  },
  "order": [
    "main",
    "related-products"
  ]
}
