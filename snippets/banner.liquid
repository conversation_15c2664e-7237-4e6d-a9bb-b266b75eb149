{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
BANNER COMPONENT
----------------------------------------------------------------------------------------------------------------------

This component is used to create banner. This is used on every form status or to convey information.

********************************************
Supported variables
********************************************

* status: can be "success", "error" or "warning" (if none is set the colors must be set on the parent)
* content: the textual content to use
* text_alignment: can be set to "left" to force aligning on left
* button_href: an optional button link
* button_content: an optional button content
{%- endcomment -%}


{%- case status -%}
  {%- when 'success' -%}
    {%- assign icon = 'success' -%}
    {%- assign button_background = settings.success_color -%}
  {%- when 'error' -%}
    {%- assign icon = 'error' -%}
    {%- assign button_background = settings.error_color -%}
  {%- when 'warning' -%}
    {%- assign icon = 'warning' -%}
    {%- assign button_background = settings.warning_color -%}
{%- endcase -%}

<div class="banner banner--{{ status }} {% if button_content != blank %}banner--with-icon{% endif %} {% if text_alignment != 'left' %}justify-center{% endif %}">
  {%- render 'icon' with icon, width: icon_width, height: icon_width, class: 'offset-icon' -%}
  {{- content -}}

  {%- if button_content != blank -%}
    {%- render 'button', href: button_href, external: true, content: button_content, size: 'sm', background: button_background -%}
  {%- endif -%}
</div>