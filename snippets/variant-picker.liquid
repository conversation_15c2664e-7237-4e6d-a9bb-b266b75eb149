{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
VARIANT PICKER
----------------------------------------------------------------------------------------------------------------------

Render the variant picker option selectors.

********************************************
Supported variables
********************************************

* product: the product for which to render the selector (required)
* form_id: the ID of the form to which the variant picker belongs (required)
* update_url: if set to true, the URL is updated when the variant changes (usually used for product page)
* hide_sold_out_variants: if set to true, the sold out or unavailable variants are hidden
* hide_size_chart: if set to true, size chart is always hidden
* force_dropdown_as_block: if set to true, the dropdown selectors are replaced by blocks, which is useful for quick buy where dropdown are not convenient to use
* block: the block itself, for theme-specific options (such as selector types)
{%- endcomment -%}

{%- assign color_label_list = 'general.label.color' | t | replace: ', ', ',' | downcase | split: ',' -%}
{%- assign size_label_list = 'general.label.size' | t | replace: ', ', ',' | downcase | split: ',' -%}
{%- assign variant_image_options = block.settings.variant_image_options | replace: ', ', ',' | downcase | split: ',' -%}

{%- unless product.has_only_default_variant -%}
  <variant-picker class="variant-picker" section-id="{{ section.id }}" handle="{{ product.handle }}" form-id="{{ form_id }}" {% if update_url %}update-url{% endif %}>
    {%- comment -%}
    The variant data is outputted as a JSON, which allows the theme to emit an event with the data when the variant changes. This must not be removed.
    {%- endcomment -%}
    <script data-variant type="application/json">
      {{- product.selected_or_first_available_variant | json -}}
    </script>

    {%- for option in product.options_with_values -%}
      {% liquid
        assign option_downcase = option.name | downcase
        assign resolved_option_selector_style = block.settings.selector_style

        assign swatch_count = option.values | map: 'swatch' | compact | size

        if swatch_count > 0 and block.settings.swatch_selector_style != 'none'
          # Use the swatch selector type only if we have at least one swatch and a supported swatch selector type
          assign resolved_option_selector_style = block.settings.swatch_selector_style
        endif

        # Implementation note: if the option value has no native swatch, BUT that the option name matches a hardcoded list of color names,
        # we fallback to the legacy config-based system. This allows to keep compatibility with stores that were using the config-based, and
        # allow those merchants to upgrade to the new system at their own pace.
        if swatch_count == 0 and color_label_list contains option_downcase and block.settings.swatch_selector_style != 'none'
          assign resolved_option_selector_style = block.settings.swatch_selector_style
        endif

        if resolved_option_selector_style == 'dropdown' and force_dropdown_as_block
          assign resolved_option_selector_style = 'block'
        endif

        if variant_image_options contains option_downcase
          assign resolved_option_selector_style = 'variant_image'
        endif
      %}

      <fieldset class="variant-picker__option">
        <div class="variant-picker__option-info">
          <div class="h-stack gap-2">
            <legend class="text-subdued">{{ option.name }}:</legend>
            <span>{{ option.selected_value }}</span>
          </div>

          {%- if hide_size_chart != true and block.settings.size_chart_page != blank and size_label_list contains option_downcase -%}
            {%- capture drawer_id -%}size-chart-{{ option.position }}-{{ form_id }}{%- endcapture -%}

            <button type="button" class="text-sm text-subdued" aria-controls="{{ drawer_id | escape }}" aria-expanded="false">
              <span class="link">{{ 'product.general.size_chart' | t }}</span>
            </button>

            <x-drawer id="{{ drawer_id }}" class="drawer drawer--lg">
              <span class="h5" slot="header">{{ block.settings.size_chart_page.title }}</span>

              <div class="prose">
                {{- block.settings.size_chart_page.content -}}
              </div>
            </x-drawer>
          {%- endif -%}
        </div>

        {%- if resolved_option_selector_style == 'dropdown' -%}
          {%- capture popover_id -%}popover-variant-picker-{{ section.id }}-{{ product.id }}-{{ option.position }}{%- endcapture -%}

          <div class="relative">
            <button type="button" class="select" aria-controls="{{ popover_id }}" aria-expanded="false">
              <span id="{{ popover_id }}-selected-value">{{- option.selected_value -}}</span>
              {%- render 'icon' with 'chevron-bottom', class: 'select-chevron' -%}
            </button>

            <x-popover id="{{ popover_id }}" class="popover" initial-focus="[aria-selected='true']" close-on-listbox-select anchor-horizontal="start" anchor-vertical="end">
              <p class="h5" slot="title">{{ option.name }}</p>

              {%- assign param_name = form_id | append: '-option' | append: option.position -%}

              <div data-option-selector class="popover-listbox">
                {%- for option_value in option.values -%}
                  {%- if hide_sold_out_variants == false or option_value.available or option_value.selected -%}
                    {%- if update_url and option_value.product_url != blank -%}
                      {%- if option_value.selected -%}
                        <input class="sr-only" type="radio" id="{{ param_name }}-{{ option_value.id }}" form="{{ form_id }}" name="{{ param_name }}" data-option-position="{{ option.position }}" value="{{ option_value.id }}" checked>
                      {%- endif -%}

                      <a href="{{ option_value.variant.url | default: option_value.product_url }}" class="popover-listbox__option {% unless option_value.available %}is-disabled{% endunless %}">
                        {{- option_value.name -}}
                      </a>
                    {%- else -%}
                      <label class="popover-listbox__option {% unless option_value.available %}is-disabled{% endunless %}" for="{{ param_name }}-{{ option_value.id }}">
                        <input class="sr-only" form="{{ form_id }}" type="radio" id="{{ param_name }}-{{ option_value.id }}" name="{{ param_name }}" {% if option_value.product_url != blank %}data-product-url="{{ option_value.product_url | escape }}"{% endif %} data-option-position="{{ option.position }}" value="{{ option_value.id }}" {% if option_value.selected %}checked{% endif %}>

                        {{- option_value.name -}}
                      </label>
                    {%- endif -%}
                  {%- endif -%}
                {%- endfor -%}
              </div>
            </x-popover>
          </div>
        {%- else -%}
          <div {% unless block.settings.stack_blocks %}class="scroll-area bleed sm:unbleed"{% endunless %}>
            <div class="variant-picker__option-values {% if block.settings.stack_blocks %}wrap{% else %}scroll-area bleed sm:unbleed{% endif %} {% if resolved_option_selector_style == 'swatch' and settings.color_swatch_style == 'rectangle' %}variant-picker__option-values--color gap-4{% else %}gap-2{% endif %}">
              {% liquid
                assign name = form_id | append: '-option' | append: option.position

                for option_value in option.values
                  case resolved_option_selector_style
                    when 'variant_image'
                      render 'option-value', type: 'thumbnail', form: form_id, option_value: option_value, param_name: name, option_position: option.position, hide_if_disabled: hide_sold_out_variants, reload_page_for_combined_products: update_url, id_prefix: forloop.index, bordered: true
                    when 'swatch'
                      render 'option-value', type: 'swatch', form: form_id, option_value: option_value, param_name: name, option_position: option.position, hide_if_disabled: hide_sold_out_variants, reload_page_for_combined_products: update_url, id_prefix: forloop.index
                    when 'block'
                      render 'option-value', type: 'block', form: form_id, option_value: option_value, param_name: name, option_position: option.position, hide_if_disabled: hide_sold_out_variants, reload_page_for_combined_products: update_url, id_prefix: forloop.index
                    when 'block_swatch'
                      render 'option-value', type: 'block', form: form_id, option_value: option_value, param_name: name, option_position: option.position, show_swatch: true, hide_if_disabled: hide_sold_out_variants, reload_page_for_combined_products: update_url, id_prefix: forloop.index
                  endcase
                endfor
              %}
            </div>
          </div>
        {%- endif -%}
      </fieldset>
    {%- endfor -%}
  </variant-picker>
{%- endunless -%}